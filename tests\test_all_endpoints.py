#!/usr/bin/env python3
"""
Comprehensive test file for all 6 endpoints in routing.py
Tests the complete Pool Service Routing API functionality
"""

import requests
import json
import time
from datetime import datetime

print("🧪 COMPREHENSIVE ENDPOINT TESTING")
print("=" * 60)
print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print("=" * 60)

BASE_URL = "http://localhost:8000"
test_results = {}

def test_endpoint(name, method, url, data=None, timeout=30):
    """Test a single endpoint and return results"""
    print(f"\n🔍 Testing: {name}")
    print(f"   Method: {method}")
    print(f"   URL: {url}")
    
    start_time = time.time()
    try:
        if method.upper() == "GET":
            response = requests.get(url, timeout=timeout)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, timeout=timeout)
        
        end_time = time.time()
        response_time = (end_time - start_time) * 1000  # Convert to ms
        
        if response.status_code == 200:
            print(f"   ✅ SUCCESS - {response.status_code}")
            print(f"   ⏱️  Response time: {response_time:.1f}ms")
            
            # Try to parse JSON response
            try:
                json_data = response.json()
                if isinstance(json_data, dict):
                    # Show key metrics for different endpoint types
                    if 'total_customers' in json_data:
                        print(f"   📊 Customers: {json_data.get('total_customers', 'N/A')}")
                        print(f"   🏗️  Zones: {json_data.get('total_zones', 'N/A')}")
                        print(f"   ⚡ Computation: {json_data.get('computation_time_ms', 'N/A')}ms")
                    elif 'routes' in json_data:
                        print(f"   🚗 Routes: {len(json_data.get('routes', []))}")
                        print(f"   📏 Total distance: {json_data.get('summary', {}).get('total_distance_km', 'N/A')}km")
                    elif 'success' in json_data:
                        print(f"   🎯 Success: {json_data.get('success', 'N/A')}")
                        print(f"   🛣️  Routable: {json_data.get('is_routable', 'N/A')}")
                    elif 'status' in json_data:
                        print(f"   💚 Status: {json_data.get('status', 'N/A')}")
            except:
                # HTML response (for map endpoints)
                content_length = len(response.content)
                print(f"   🗺️  HTML content: {content_length} bytes")
                if "Dynamic Routes Map" in response.text:
                    print(f"   🎯 Map type: Dynamic Routes (thick lines)")
                elif "weight: 6" in response.text:
                    print(f"   ✅ Thick lines confirmed (weight: 6)")
            
            return {"status": "SUCCESS", "code": response.status_code, "time_ms": response_time}
        else:
            print(f"   ❌ FAILED - {response.status_code}")
            print(f"   📝 Error: {response.text[:100]}...")
            return {"status": "FAILED", "code": response.status_code, "time_ms": response_time}
            
    except requests.exceptions.Timeout:
        print(f"   ⏰ TIMEOUT - Request took longer than {timeout}s")
        return {"status": "TIMEOUT", "code": None, "time_ms": timeout * 1000}
    except Exception as e:
        print(f"   💥 ERROR - {str(e)}")
        return {"status": "ERROR", "code": None, "time_ms": 0}

# Test all 6 endpoints
print("\n🚀 TESTING ALL 6 ENDPOINTS")
print("-" * 40)

# 1. Root endpoint
test_results["root"] = test_endpoint(
    "Root Documentation", 
    "GET", 
    f"{BASE_URL}/"
)

# 2. Health check
test_results["health"] = test_endpoint(
    "Health Check", 
    "GET", 
    f"{BASE_URL}/health"
)

# 3. Dynamic zoning
test_results["zoning"] = test_endpoint(
    "Dynamic Zoning", 
    "GET", 
    f"{BASE_URL}/api/zoning",
    timeout=120  # Longer timeout for processing
)

# 4. Address processing
test_results["address"] = test_endpoint(
    "Address Processing", 
    "POST", 
    f"{BASE_URL}/api/process-address",
    data={
        "address": "123 Main Street, San Antonio, TX",
        "longitude": -98.5,
        "latitude": 29.4
    }
)

# 5. Route optimization
test_results["optimization"] = test_endpoint(
    "Route Optimization", 
    "POST", 
    f"{BASE_URL}/api/dynamic-routes/optimize",
    timeout=300  # Longer timeout for optimization
)

# 6. Interactive map
test_results["map"] = test_endpoint(
    "Interactive Map (Thick Lines)", 
    "GET", 
    f"{BASE_URL}/api/dynamic-routes/map",
    timeout=60
)

# Summary Report
print("\n" + "=" * 60)
print("📋 COMPREHENSIVE TEST SUMMARY")
print("=" * 60)

success_count = 0
total_time = 0

for endpoint, result in test_results.items():
    status_icon = "✅" if result["status"] == "SUCCESS" else "❌"
    print(f"{status_icon} {endpoint.upper():<15} - {result['status']:<8} ({result['code']}) - {result['time_ms']:.1f}ms")
    
    if result["status"] == "SUCCESS":
        success_count += 1
    total_time += result["time_ms"]

print("-" * 60)
print(f"📊 SUCCESS RATE: {success_count}/6 ({(success_count/6)*100:.1f}%)")
print(f"⏱️  TOTAL TIME: {total_time:.1f}ms")
print(f"⚡ AVERAGE TIME: {total_time/6:.1f}ms per endpoint")

# Specific recommendations
print("\n🎯 ENDPOINT STATUS:")
if test_results["zoning"]["status"] == "SUCCESS":
    print("✅ Dynamic zoning is working (Google Maps-like processing)")
if test_results["optimization"]["status"] == "SUCCESS":
    print("✅ Route optimization is working (real distances)")
if test_results["map"]["status"] == "SUCCESS":
    print("✅ Interactive map is working (thick route lines)")
if test_results["address"]["status"] == "SUCCESS":
    print("✅ Address processing is working (geocoding & snapping)")

print("\n🗺️ MAP ACCESS:")
if test_results["map"]["status"] == "SUCCESS":
    print(f"🌐 View the interactive map at: {BASE_URL}/api/dynamic-routes/map")
    print("🎨 Features: Thick route lines, customer markers, route statistics")

print("\n🚀 API is ready for production deployment!")
print(f"📅 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
