#!/usr/bin/env python3
"""
Debug script to investigate why Technician 9 routes are not being formed
"""

import requests
import json

print("🔍 DEBUGGING TECHNICIAN 9 ROUTE FORMATION")
print("=" * 60)

BASE_URL = "http://localhost:8000"

# Step 1: Get dynamic zoning data to see Technician 9's assignment
print("1. Getting dynamic zoning data...")
try:
    response = requests.get(f"{BASE_URL}/api/zoning")
    if response.status_code == 200:
        zoning_data = response.json()
        print(f"   ✅ Total zones: {zoning_data['total_zones']}")
        print(f"   📊 Total customers: {zoning_data['total_customers']}")
        
        # Find Technician 9 (Zone 9)
        tech_9_zone = None
        for zone in zoning_data['zones']:
            if zone['zone_id'] == 9:
                tech_9_zone = zone
                break
        
        if tech_9_zone:
            print(f"\n🎯 TECHNICIAN 9 DETAILS:")
            print(f"   Zone ID: {tech_9_zone['zone_id']}")
            print(f"   Customer count: {tech_9_zone['customer_count']}")
            print(f"   Customers assigned: {len(tech_9_zone['customers'])}")
            
            print(f"\n📍 TECHNICIAN 9 CUSTOMERS:")
            for i, customer in enumerate(tech_9_zone['customers']):
                print(f"   {i+1}. Customer {customer['id']}: [{customer['longitude']:.6f}, {customer['latitude']:.6f}]")
                
            # Check if coordinates are valid
            valid_coords = True
            for customer in tech_9_zone['customers']:
                if not (-180 <= customer['longitude'] <= 180) or not (-90 <= customer['latitude'] <= 90):
                    print(f"   ❌ Invalid coordinates for Customer {customer['id']}")
                    valid_coords = False
            
            if valid_coords:
                print(f"   ✅ All coordinates are valid")
            
            # Calculate zone center
            lats = [c['latitude'] for c in tech_9_zone['customers']]
            lons = [c['longitude'] for c in tech_9_zone['customers']]
            center_lat = sum(lats) / len(lats)
            center_lon = sum(lons) / len(lons)
            print(f"   🎯 Zone center: [{center_lon:.6f}, {center_lat:.6f}]")
            
        else:
            print(f"   ❌ Technician 9 (Zone 9) not found in zoning data")
            print(f"   Available zones: {[zone['zone_id'] for zone in zoning_data['zones']]}")
    else:
        print(f"   ❌ Failed to get zoning data: {response.status_code}")
        
except Exception as e:
    print(f"   💥 Error getting zoning data: {e}")

# Step 2: Test route optimization and check for Technician 9
print(f"\n2. Testing route optimization...")
try:
    response = requests.post(f"{BASE_URL}/api/dynamic-routes/optimize", timeout=300)
    if response.status_code == 200:
        optimization_data = response.json()
        print(f"   ✅ Optimization successful")
        print(f"   🚗 Total routes created: {len(optimization_data.get('routes', []))}")
        
        # Look for Technician 9 in the results
        tech_9_route = None
        for route in optimization_data.get('routes', []):
            if route.get('technician_id') == 9:
                tech_9_route = route
                break
        
        if tech_9_route:
            print(f"\n✅ TECHNICIAN 9 ROUTE FOUND:")
            print(f"   Technician ID: {tech_9_route['technician_id']}")
            print(f"   Customers: {len(tech_9_route.get('customers', []))}")
            print(f"   Distance: {tech_9_route.get('total_distance_km', 0)} km")
            print(f"   Duration: {tech_9_route.get('total_duration_minutes', 0)} minutes")
            print(f"   Has geometry: {'route_geometry' in tech_9_route and tech_9_route['route_geometry'] is not None}")
        else:
            print(f"\n❌ TECHNICIAN 9 ROUTE NOT FOUND")
            print(f"   Available technician IDs: {[route.get('technician_id') for route in optimization_data.get('routes', [])]}")
            
            # Check if there are any routes at all
            if optimization_data.get('routes'):
                print(f"\n📋 SAMPLE ROUTE (for comparison):")
                sample_route = optimization_data['routes'][0]
                print(f"   Technician ID: {sample_route.get('technician_id')}")
                print(f"   Customers: {len(sample_route.get('customers', []))}")
                print(f"   Distance: {sample_route.get('total_distance_km', 0)} km")
            else:
                print(f"   ❌ No routes were created at all!")
                
    else:
        print(f"   ❌ Optimization failed: {response.status_code}")
        print(f"   Error: {response.text}")
        
except Exception as e:
    print(f"   💥 Error during optimization: {e}")

# Step 3: Check if the issue is with the map display
print(f"\n3. Checking map display...")
try:
    response = requests.get(f"{BASE_URL}/api/dynamic-routes/map")
    if response.status_code == 200:
        map_content = response.text
        if "Technician 9" in map_content:
            print(f"   ✅ Technician 9 found in map content")
        else:
            print(f"   ❌ Technician 9 NOT found in map content")
            
        # Count how many technicians are in the map
        tech_count = map_content.count("Technician ")
        print(f"   📊 Total technicians in map: {tech_count}")
        
    else:
        print(f"   ❌ Map failed to load: {response.status_code}")
        
except Exception as e:
    print(f"   💥 Error checking map: {e}")

print(f"\n" + "=" * 60)
print("🎯 DIAGNOSIS SUMMARY:")
print("Check the output above to identify why Technician 9 routes are not formed:")
print("1. Is Technician 9 assigned customers in zoning?")
print("2. Are the coordinates valid?") 
print("3. Is the route optimization failing for Technician 9?")
print("4. Is Technician 9 missing from the final results?")
print("=" * 60)
